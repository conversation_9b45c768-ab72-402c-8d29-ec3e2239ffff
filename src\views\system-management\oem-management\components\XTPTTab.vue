<template>
    <div>
        <div class="flex top-bottom-center gap-8 b-margin-16">
            <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
            <span class="color-black font-16">登录页</span>
        </div>
        <div class="flex space-between top-bottom-center t-margin-16">
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>域名</span>
                <el-input
                    v-model="yuming"
                    placeholder="请输入域名"
                ></el-input>
            </div>
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span class="color-two-grey font-16">
                    底部时间
                    <span class="color-three-grey font-14">(默认值为2016-2025)</span>
                </span>
                <el-date-picker
                    v-model="cDateValue"
                    type="yearrange"
                    range-separator="To"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 100%;"
                />
            </div>
        </div>
        <div class="flex space-between top-bottom-center t-margin-16">
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>
                    底部公司名
                    <span class="color-three-grey font-14">(默认值为数组科技（南京）股份有限公司)</span>
                </span>
                <el-input
                    v-model="cName"
                    placeholder="请输入公司名"
                ></el-input>
            </div>
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>
                    左侧顶部文字
                    <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                </span>
                <el-input
                    v-model="companyName"
                    placeholder="请输入顶部文字"
                ></el-input>
            </div>
        </div>
        <div class="flex space-between top-bottom-center t-margin-16">
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>左上角logo</span>
                <el-upload
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleLogoProgress"
                    :on-success="handleLogoSuccess"
                >
                    <img v-if="loginLogoUrl && !loginLogoLoading" :src="loginLogoUrl" class="avatar" />
                    <el-icon v-else-if="loginLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
            </div>
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>登录页图片</span>
                <el-upload
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleLoginProgress"
                    :on-success="handleLoginSuccess"
                >
                    <img v-if="logoImgUrl && !logoImgLoading" :src="logoImgUrl" class="avatar" />
                    <el-icon v-else-if="logoImgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                </el-upload>
            </div>
        </div>
        <div class="flex space-between top-bottom-center t-margin-16">
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>
                    网页页签标题
                    <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                </span>
                <el-input
                    v-model="yeqianbiaoti"
                    placeholder="请输入网页页签标题"
                ></el-input>
            </div>
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>底部的备案号</span>
                <el-input
                    v-model="TCP"
                    placeholder="请输入备案号"
                ></el-input>
            </div>
        </div>
        <div class="flex top-bottom-center gap-8 tb-margin-16">
            <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
            <span class="color-black font-16">首页</span>
        </div>
        <div class="flex space-between top-bottom-center t-margin-16">
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>首页上方的logo</span>
                <el-upload
                    class="avatar-uploader"
                    :action="uploadFile"
                    :headers="headers"
                    accept="image/jpeg,image/png,image/jpg"
                    :show-file-list="false"
                    :on-progress="handleHomeLogoProgress"
                    :on-success="handleHomeLogoSuccess"
                >
                    <img v-if="logoBkUrl && !logoBkLoading" :src="logoBkUrl" class="avatar" />
                    <el-icon v-else-if="logoBkLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                </el-upload>
            </div>
            <div class="flex flex-column gap-8" style="width: 48%;">
                <span>帮助手册地址</span>
                <el-input
                    v-model="helpUrl"
                    placeholder="请输入帮助手册地址"
                ></el-input>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, watch } from 'vue'
import type { UploadProps } from 'element-plus'
import type { OEMUploadItem } from '@/types/OEM'

// Props
const props = defineProps<{
    updateData: OEMUploadItem
    uploadFile: string
    headers: Record<string, string>
}>()

// 表单数据
const yuming = ref('')
const cName = ref('')
const companyName = ref('')
const yeqianbiaoti = ref('')
const TCP = ref('')
const helpUrl = ref('')

// 左上角logo上传相关
const loginLogoUrl = ref('')
const loginLogoLoading = ref(false)
const handleLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    loginLogoLoading.value = true
}
const handleLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    loginLogoLoading.value = false
    loginLogoUrl.value = response.data.link
}

// 登录页图片上传相关
const logoImgUrl = ref('')
const logoImgLoading = ref(false)
const handleLoginProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoImgLoading.value = true
}
const handleLoginSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response)
    logoImgLoading.value = false
    logoImgUrl.value = response.data.link
}

// 首页上方的logo上传相关
const logoBkUrl = ref('')
const logoBkLoading = ref(false)
const handleHomeLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoBkLoading.value = true
}
const handleHomeLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response)
    logoBkLoading.value = false
    logoBkUrl.value = response.data.link
}

// cDate转化
const cDateValue = ref([] as Date[])
const cDate = computed(() => {
    if(cDateValue.value && cDateValue.value.length === 2) {
        return transformDate(cDateValue.value)
    }else{
        return ''
    }
})
const transformDate = (data : Date[]) => {
    const startYear = data[0].getFullYear()
    const endYear = data[1].getFullYear()
    return `${startYear}-${endYear}`
}

// 监听数据变化，更新父组件的updateData
watch([cDate, cName, companyName, yeqianbiaoti, TCP, helpUrl, loginLogoUrl, logoImgUrl, logoBkUrl], () => {
    // 确保 config 对象存在
    if (!props.updateData.modules[0].config) {
        props.updateData.modules[0].config = {}
    }
    
    props.updateData.modules[0].config.cDate = cDate.value
    props.updateData.modules[0].config.cName = cName.value
    props.updateData.modules[0].config.companyName = companyName.value
    props.updateData.modules[0].config.TCP = TCP.value
    props.updateData.modules[0].config.helpUrl = helpUrl.value
    props.updateData.modules[0].config.loginLogo = loginLogoUrl.value
    props.updateData.modules[0].config.logoImg = logoImgUrl.value
    props.updateData.modules[0].config.logoBk = logoBkUrl.value

}, { immediate: true, deep: true })

</script>

<style lang='scss' scoped>
:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}
</style>
