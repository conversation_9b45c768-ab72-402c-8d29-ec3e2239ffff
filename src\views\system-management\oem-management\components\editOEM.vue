<template>
    <div class="all-paddding-16">
        <div>
            <div class="border"></div>
            <div class="flex top-bottom-center gap-8 t-margin-16">
                <span class="font-24 flex top-bottom-center" style="color: #D66353;">*</span>
                <span class="color-two-grey font-16">oem_key</span>
                <el-input
                    v-model="oem_key"
                    placeholder="请输入渠道名"
                    style="width: 20%;"
                ></el-input>
            </div>
        </div>
        <el-tabs v-model="activeName" class="demo-tabs t-margin-16" >
            <el-tab-pane label="臻企云·数字化产业融合协同平台" name="xtpt">
                <div class="flex top-bottom-center gap-8 b-margin-16">
                    <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
                    <span class="color-black font-16">登录页</span>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>域名</span>
                        <el-input
                            v-model="yuming"
                            placeholder="请输入域名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">
                            底部时间
                            <span class="color-three-grey font-14">(默认值为2016-2025)</span>
                        </span>
                        <el-date-picker
                            v-model="cDateValue"
                            type="yearrange"
                            range-separator="To"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            style="width: 100%;"
                        />
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>
                            底部公司名
                            <span class="color-three-grey font-14">(默认值为数组科技（南京）股份有限公司)</span>
                        </span>
                        <el-input
                            v-model="cName"
                            placeholder="请输入公司名"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>
                            左侧顶部文字
                            <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                        </span>
                        <el-input
                            v-model="companyName"
                            placeholder="请输入顶部文字"
                        ></el-input>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>左上角logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleLogoProgress"
                            :on-success="handleLogoSuccess"
                        >
                            <img v-if="loginLogoUrl && !loginLogoLoading" :src="loginLogoUrl" class="avatar" />
                            <el-icon v-else-if="loginLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>登录页图片</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleLoginProgress"
                            :on-success="handleLoginSuccess"
                        >
                            <img v-if="logoImgUrl && !logoImgLoading" :src="logoImgUrl" class="avatar" />
                            <el-icon v-else-if="logoImgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>
                            网页页签标题
                            <span class="color-three-grey font-14">(默认值为臻企云·数字化产业融合协同平台)</span>
                        </span>
                        <el-input
                            v-model="yeqianbiaoti"
                            placeholder="请输入网页页签标题"
                        ></el-input>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>底部的备案号</span>
                        <el-input
                            v-model="TCP"
                            placeholder="请输入备案号"
                        ></el-input>
                    </div>
                </div>
                <div class="flex top-bottom-center gap-8 tb-margin-16">
                    <div style="width: 4px;height: 14px; background-color: #1966FF;" ></div>
                    <span class="color-black font-16">首页</span>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页上方的logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleHomeLogoProgress"
                            :on-success="handleHomeLogoSuccess"
                        >
                            <img v-if="logoBkUrl && !logoBkLoading" :src="logoBkUrl" class="avatar" />
                            <el-icon v-else-if="logoBkLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>帮助手册地址</span>
                        <el-input
                            v-model="helpUrl"
                            placeholder="请输入帮助手册地址"
                        ></el-input>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="经营慧眼" name="jyhy">
                <div class="flex space-between top-bottom-center">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页logo</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleMpLogoProgress"
                            :on-success="handleMpLogoSuccess"
                        >
                            <img v-if="mpLogoUrl && !mpLogoLoading" :src="mpLogoUrl" class="avatar" />
                            <el-icon v-else-if="mpLogoLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页顶部图</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleMpHomeBannerProgress"
                            :on-success="handleMpHomeBannerSuccess"
                        >
                            <img v-if="mpHomeBannerUrl && !mpHomeBannerLoading" :src="mpHomeBannerUrl" class="avatar" />
                            <el-icon v-else-if="mpHomeBannerLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>首页搜索框下方图</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleMpHomeSpecProgress"
                            :on-success="handleMpHomeSpecSuccess"
                        >
                            <img v-if="mpHomeSpecUrl && !mpHomeSpecLoading" :src="mpHomeSpecUrl" class="avatar" />
                            <el-icon v-else-if="mpHomeSpecLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">是否开始精简授权</span>
                        <el-switch v-model="easyCollect" />
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>是否开启票易融</span>
                        <el-switch v-model="isPYR" />
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">
                            业务进件二维码海报图
                        </span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleJinrongEduApplyBackimgProgress"
                            :on-success="handleJinrongEduApplyBackimgSuccess"
                        >
                            <img v-if="jinrongEduApplyBackimgUrl && !jinrongEduApplyBackimgLoading" :src="jinrongEduApplyBackimgUrl" class="avatar" />
                            <el-icon v-else-if="jinrongEduApplyBackimgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>业务进件分享图</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleJinrongEduShareBackimgProgress"
                            :on-success="handleJinrongEduShareBackimgSuccess"
                        >
                            <img v-if="jinrongEduShareBackimgUrl && !jinrongEduShareBackimgLoading" :src="jinrongEduShareBackimgUrl" class="avatar" />
                            <el-icon v-else-if="jinrongEduShareBackimgLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span class="color-two-grey font-16">
                            业务进件二维码信息
                            <span class="color-three-grey font-14">(大小，距离顶部，距离左边(50,0,0))</span>
                        </span>
                        <el-input
                            v-model="jinrongEduApplyQrcode"
                            placeholder="请输大小，距离顶部，距离左边(50,0,0)"
                        ></el-input>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="报告" name="bg">
                <div class="flex space-between top-bottom-center">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>高企报告封面</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleGqCoverProgress"
                            :on-success="handleGqCoverSuccess"
                        >
                            <img v-if="gqCoverUrl && !gqCoverLoading" :src="gqCoverUrl" class="avatar" />
                            <el-icon v-else-if="gqCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                    
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>税务报告封面</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleSwCoverProgress"
                            :on-success="handleSwCoverSuccess"
                        >
                            <img v-if="swCoverUrl && !swCoverLoading" :src="swCoverUrl" class="avatar" />
                            <el-icon v-else-if="swCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                        </el-upload>
                    </div>
                </div>
                <div class="flex space-between top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>发票报告封面</span>
                        <el-upload
                            class="avatar-uploader"
                            :action="uploadFile"
                            :headers="headers"
                            accept="image/jpeg,image/png,image/jpg"
                            :show-file-list="false"
                            :on-progress="handleFpCoverProgress"
                            :on-success="handleFpCoverSuccess"
                        >
                            <img v-if="fpCoverUrl && !fpCoverLoading" :src="fpCoverUrl" class="avatar" />
                            <el-icon v-else-if="fpCoverLoading" class="avatar-uploader-icon"><Loading /></el-icon>
                            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>

                        </el-upload>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="其他" name="other">
                <div class="flex space-between top-bottom-center">
                    <div class="flex flex-column gap-8" style="width: 48%;">
                        <span>数据大屏标题</span>
                        <el-input
                            v-model="yuming"
                            placeholder="请输入域名"
                        ></el-input>
                    </div>
                </div>
                <div class="flex top-bottom-center t-margin-16">
                    <div class="flex flex-column gap-8" style="width: 100%;">
                        <span>授权协议</span>
                        <div ref="quillEditor" style="height: 300px;"></div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="save()" :loading="loading" >
                确定
            </el-button>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import type { UploadProps } from 'element-plus'
import type { OEMUploadItem, XTPT, JYHY, bgCover, other } from '@/types/OEM'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

// Quill富文本编辑器相关
const quillEditor = ref<HTMLElement | null>(null)
const quillInstance = ref<Quill | null>(null)
const authAgreement = ref('')

// 初始化Quill编辑器
const initQuillEditor = () => {
    if (quillEditor.value) {
        quillInstance.value = new Quill(quillEditor.value, {
            theme: 'snow',
            placeholder: '请输入授权协议内容...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'align': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['link', 'image'],
                    ['clean']
                ]
            }
        })

        // 监听内容变化
        quillInstance.value.on('text-change', () => {
            if (quillInstance.value) {
                authAgreement.value = quillInstance.value.root.innerHTML
            }
        })

        // 设置初始内容
        if (authAgreement.value) {
            quillInstance.value.root.innerHTML = authAgreement.value
        }
    }
}

const loading = ref(false)
const activeName = ref('xtpt')

// 上传文件接口
const uploadFile = `/api/zhenqi-crm/file/upload`
// 请求头
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}

// ======协同平台======
// cDate转化
const cDateValue = ref([] as Date[])
const cDate = computed(() => {
    if(cDateValue.value && cDateValue.value.length === 2) {
        return transformDate(cDateValue.value)
    }else{
        return ''
    }
})
const transformDate = (data : Date[]) => {
    const startYear = data[0].getFullYear()
    const endYear = data[1].getFullYear()
    return `${startYear}-${endYear}`
}
const oem_key = ref('')
const yuming = ref('')
const cName = ref('')
const companyName = ref('')
const yeqianbiaoti = ref('')
const TCP = ref('')
const helpUrl = ref('')

// 左上角logo上传相关
const loginLogoUrl = ref('')
const loginLogoLoading = ref(false)
const handleLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    loginLogoLoading.value = true
}
const handleLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    loginLogoLoading.value = false
    loginLogoUrl.value = response.data.link
}

// 登录页图片上传相关
const logoImgUrl = ref('')
const logoImgLoading = ref(false)
const handleLoginProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoImgLoading.value = true
}
const handleLoginSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, uploadFile)
    logoImgLoading.value = false
    logoImgUrl.value = response.data.link
}

// 首页上方的logo上传相关
const logoBkUrl = ref('')
const logoBkLoading = ref(false)
const handleHomeLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    logoBkLoading.value = true
}
const handleHomeLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response)
    logoBkLoading.value = false
    logoBkUrl.value = response.data.link
}

// ======经营慧眼======
const easyCollect = ref(false)
const isPYR = ref(false)
const jinrongEduApplyQrcode = ref('')

// 首页logo上传相关
const mpLogoUrl = ref('')
const mpLogoLoading = ref(false)
const handleMpLogoProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpLogoLoading.value = true
}
const handleMpLogoSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpLogoLoading.value = false
    mpLogoUrl.value = response.data.link
}

// 首页顶部图上传相关
const mpHomeBannerUrl = ref('')
const mpHomeBannerLoading = ref(false)
const handleMpHomeBannerProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpHomeBannerLoading.value = true
}
const handleMpHomeBannerSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpHomeBannerLoading.value = false
    mpHomeBannerUrl.value = response.data.link
}

// 首页搜索框下方图上传相关
const mpHomeSpecUrl = ref('')
const mpHomeSpecLoading = ref(false)
const handleMpHomeSpecProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    mpHomeSpecLoading.value = true
}
const handleMpHomeSpecSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    mpHomeSpecLoading.value = false
    mpHomeSpecUrl.value = response.data.link
}

// 业务进件二维码海报图上传相关
const jinrongEduApplyBackimgUrl = ref('')
const jinrongEduApplyBackimgLoading = ref(false)
const handleJinrongEduApplyBackimgProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    jinrongEduApplyBackimgLoading.value = true
}
const handleJinrongEduApplyBackimgSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    jinrongEduApplyBackimgLoading.value = false
    jinrongEduApplyBackimgUrl.value = response.data.link
}

// 业务进件分享图上传相关
const jinrongEduShareBackimgUrl = ref('')
const jinrongEduShareBackimgLoading = ref(false)
const handleJinrongEduShareBackimgProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    jinrongEduShareBackimgLoading.value = true
}
const handleJinrongEduShareBackimgSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    jinrongEduShareBackimgLoading.value = false
    jinrongEduShareBackimgUrl.value = response.data.link
}

// ======报告======
// 高企报告封面上传相关
const gqCoverUrl = ref('')
const gqCoverLoading = ref(false)
const handleGqCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    gqCoverLoading.value = true
}
const handleGqCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    gqCoverLoading.value = false
    gqCoverUrl.value = response.data.link
}

// 税务报告封面上传相关
const swCoverUrl = ref('')
const swCoverLoading = ref(false)
const handleSwCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    swCoverLoading.value = true
}
const handleSwCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    swCoverLoading.value = false
    swCoverUrl.value = response.data.link
}

// 发票报告封面上传相关
const fpCoverUrl = ref('')
const fpCoverLoading = ref(false)
const handleFpCoverProgress: UploadProps['onProgress'] = (event, file, fileList) => {
    console.log(event, file, fileList)
    fpCoverLoading.value = true
}
const handleFpCoverSuccess: UploadProps['onSuccess'] = (
    response,
) => {
    console.log(response, )
    fpCoverLoading.value = false
    fpCoverUrl.value = response.data.link
}



// 传参
const updateData = ref<OEMUploadItem>({
    key:'',
    modules:[
        {
            productType:0,
            config:{
                cDate: '',
                cName:'',
                companyName:'',
                loginLogo:'',
                logoImg:'',
                TCP:'',
                logoBk:'',
                helpUrl:'',
                authAgreement:''
            }
        },
        {
            productType:1,
            config:{
                mpLogo:'',
                mpHomeBanner:'',
                mpHomeSpec:'',
                easyCollect:false,
                jinrongEduApplyBackimg:'',
                jinrongEduShareBackimg:'',
                jinrongEduApplyQrcode:'',
            }
        },
        {
            productType:2,
            config:{
                gqbg_cover:'',
                swbg_cover:'',
                fpbg_cover:'',
            }
        },
        {
            productType:3,
            config:{

            }
        },
    ]
})

watch(oem_key, () => updateData.value.key = oem_key.value, { immediate: true, deep: true })

// 协同平台相关
watch([cDate, cName, companyName, yeqianbiaoti, TCP, helpUrl, loginLogoUrl, logoImgUrl, logoBkUrl, authAgreement], () => {
    // 确保 config 对象存在
    if (!updateData.value.modules[0].config) {
        updateData.value.modules[0].config = {}
    }
    // 断言
    const xtptConfig = updateData.value.modules[0].config as XTPT

    xtptConfig.cDate = cDate.value
    xtptConfig.cName = cName.value
    xtptConfig.companyName = companyName.value
    xtptConfig.TCP = TCP.value
    xtptConfig.helpUrl = helpUrl.value
    xtptConfig.loginLogo = loginLogoUrl.value
    xtptConfig.logoImg = logoImgUrl.value
    xtptConfig.logoBk = logoBkUrl.value
    xtptConfig.authAgreement = authAgreement.value

}, { immediate: true, deep: true })

// 经营慧眼相关
watch([mpLogoUrl, mpHomeBannerUrl, mpHomeSpecUrl, easyCollect, isPYR, jinrongEduApplyBackimgUrl, jinrongEduShareBackimgUrl, jinrongEduApplyQrcode], () => {
    // 确保 config 对象存在
    if (!updateData.value.modules[1].config) {
        updateData.value.modules[1].config = {}
    }
    // 断言
    const jyhyConfig = updateData.value.modules[1].config as JYHY
    jyhyConfig.mpLogo = mpLogoUrl.value
    jyhyConfig.mpHomeBanner = mpHomeBannerUrl.value
    jyhyConfig.mpHomeSpec = mpHomeSpecUrl.value
    jyhyConfig.easyCollect = easyCollect.value
    jyhyConfig.jinrongEduApplyBackimg = jinrongEduApplyBackimgUrl.value
    jyhyConfig.jinrongEduShareBackimg = jinrongEduShareBackimgUrl.value
    jyhyConfig.jinrongEduApplyQrcode = jinrongEduApplyQrcode.value
})

// 报告相关
watch([gqCoverUrl, swCoverUrl, fpCoverUrl], () => {
    if (!updateData.value.modules[1].config) {
        updateData.value.modules[1].config = {}
    }
    const bgConfig = updateData.value.modules[2].config as bgCover
    bgConfig.gqbg_cover = gqCoverUrl.value
    bgConfig.swbg_cover = swCoverUrl.value
    bgConfig.fpbg_cover = fpCoverUrl.value
})


const save = () => {
    console.log('123123',updateData.value)
}

const emit = defineEmits(['cancel'])
const handleClose = () => {
    emit('cancel')
}


// 组件挂载后初始化编辑器
onMounted(() => {
    nextTick(() => {
        initQuillEditor()
    })
})

</script>

<style lang='scss' scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}


.dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    margin-top: 20px;
}


:deep(.avatar-uploader .avatar) {
  width: 44px;
  height: 44px;
  display: block;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

:deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 44px;
  height: 44px;
  text-align: center;
}

// Quill编辑器样式
:deep(.ql-toolbar) {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-bottom: none;
}

:deep(.ql-container) {
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-top: none;
  font-size: 14px;
}

:deep(.ql-editor) {
  min-height: 200px;
  line-height: 1.6;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

</style>